/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as UsersRouteImport } from './routes/users'
import { Route as RedirectRouteImport } from './routes/redirect'
import { Route as PostsRouteImport } from './routes/posts'
import { Route as DeferredRouteImport } from './routes/deferred'
import { Route as PathlessLayoutRouteImport } from './routes/_pathlessLayout'
import { Route as IndexRouteImport } from './routes/index'
import { Route as UsersIndexRouteImport } from './routes/users.index'
import { Route as PostsIndexRouteImport } from './routes/posts.index'
import { Route as UsersUserIdRouteImport } from './routes/users.$userId'
import { Route as PostsPostIdRouteImport } from './routes/posts.$postId'
import { Route as PathlessLayoutNestedLayoutRouteImport } from './routes/_pathlessLayout/_nested-layout'
import { Route as PostsPostIdDeepRouteImport } from './routes/posts_.$postId.deep'
import { Route as PathlessLayoutNestedLayoutRouteBRouteImport } from './routes/_pathlessLayout/_nested-layout/route-b'
import { Route as PathlessLayoutNestedLayoutRouteARouteImport } from './routes/_pathlessLayout/_nested-layout/route-a'
import { ServerRoute as CustomScriptDotjsServerRouteImport } from './routes/customScript[.]js'
import { ServerRoute as ApiUsersServerRouteImport } from './routes/api/users'
import { ServerRoute as ApiUsersUserIdServerRouteImport } from './routes/api/users.$userId'

const rootServerRouteImport = createServerRootRoute()

const UsersRoute = UsersRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => rootRouteImport,
} as any)
const RedirectRoute = RedirectRouteImport.update({
  id: '/redirect',
  path: '/redirect',
  getParentRoute: () => rootRouteImport,
} as any)
const PostsRoute = PostsRouteImport.update({
  id: '/posts',
  path: '/posts',
  getParentRoute: () => rootRouteImport,
} as any)
const DeferredRoute = DeferredRouteImport.update({
  id: '/deferred',
  path: '/deferred',
  getParentRoute: () => rootRouteImport,
} as any)
const PathlessLayoutRoute = PathlessLayoutRouteImport.update({
  id: '/_pathlessLayout',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const UsersIndexRoute = UsersIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => UsersRoute,
} as any)
const PostsIndexRoute = PostsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => PostsRoute,
} as any)
const UsersUserIdRoute = UsersUserIdRouteImport.update({
  id: '/$userId',
  path: '/$userId',
  getParentRoute: () => UsersRoute,
} as any)
const PostsPostIdRoute = PostsPostIdRouteImport.update({
  id: '/$postId',
  path: '/$postId',
  getParentRoute: () => PostsRoute,
} as any)
const PathlessLayoutNestedLayoutRoute =
  PathlessLayoutNestedLayoutRouteImport.update({
    id: '/_nested-layout',
    getParentRoute: () => PathlessLayoutRoute,
  } as any)
const PostsPostIdDeepRoute = PostsPostIdDeepRouteImport.update({
  id: '/posts_/$postId/deep',
  path: '/posts/$postId/deep',
  getParentRoute: () => rootRouteImport,
} as any)
const PathlessLayoutNestedLayoutRouteBRoute =
  PathlessLayoutNestedLayoutRouteBRouteImport.update({
    id: '/route-b',
    path: '/route-b',
    getParentRoute: () => PathlessLayoutNestedLayoutRoute,
  } as any)
const PathlessLayoutNestedLayoutRouteARoute =
  PathlessLayoutNestedLayoutRouteARouteImport.update({
    id: '/route-a',
    path: '/route-a',
    getParentRoute: () => PathlessLayoutNestedLayoutRoute,
  } as any)
const CustomScriptDotjsServerRoute = CustomScriptDotjsServerRouteImport.update({
  id: '/customScript.js',
  path: '/customScript.js',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiUsersServerRoute = ApiUsersServerRouteImport.update({
  id: '/api/users',
  path: '/api/users',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiUsersUserIdServerRoute = ApiUsersUserIdServerRouteImport.update({
  id: '/$userId',
  path: '/$userId',
  getParentRoute: () => ApiUsersServerRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/deferred': typeof DeferredRoute
  '/posts': typeof PostsRouteWithChildren
  '/redirect': typeof RedirectRoute
  '/users': typeof UsersRouteWithChildren
  '/posts/$postId': typeof PostsPostIdRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/posts/': typeof PostsIndexRoute
  '/users/': typeof UsersIndexRoute
  '/route-a': typeof PathlessLayoutNestedLayoutRouteARoute
  '/route-b': typeof PathlessLayoutNestedLayoutRouteBRoute
  '/posts/$postId/deep': typeof PostsPostIdDeepRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/deferred': typeof DeferredRoute
  '/redirect': typeof RedirectRoute
  '/posts/$postId': typeof PostsPostIdRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/posts': typeof PostsIndexRoute
  '/users': typeof UsersIndexRoute
  '/route-a': typeof PathlessLayoutNestedLayoutRouteARoute
  '/route-b': typeof PathlessLayoutNestedLayoutRouteBRoute
  '/posts/$postId/deep': typeof PostsPostIdDeepRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_pathlessLayout': typeof PathlessLayoutRouteWithChildren
  '/deferred': typeof DeferredRoute
  '/posts': typeof PostsRouteWithChildren
  '/redirect': typeof RedirectRoute
  '/users': typeof UsersRouteWithChildren
  '/_pathlessLayout/_nested-layout': typeof PathlessLayoutNestedLayoutRouteWithChildren
  '/posts/$postId': typeof PostsPostIdRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/posts/': typeof PostsIndexRoute
  '/users/': typeof UsersIndexRoute
  '/_pathlessLayout/_nested-layout/route-a': typeof PathlessLayoutNestedLayoutRouteARoute
  '/_pathlessLayout/_nested-layout/route-b': typeof PathlessLayoutNestedLayoutRouteBRoute
  '/posts_/$postId/deep': typeof PostsPostIdDeepRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/deferred'
    | '/posts'
    | '/redirect'
    | '/users'
    | '/posts/$postId'
    | '/users/$userId'
    | '/posts/'
    | '/users/'
    | '/route-a'
    | '/route-b'
    | '/posts/$postId/deep'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/deferred'
    | '/redirect'
    | '/posts/$postId'
    | '/users/$userId'
    | '/posts'
    | '/users'
    | '/route-a'
    | '/route-b'
    | '/posts/$postId/deep'
  id:
    | '__root__'
    | '/'
    | '/_pathlessLayout'
    | '/deferred'
    | '/posts'
    | '/redirect'
    | '/users'
    | '/_pathlessLayout/_nested-layout'
    | '/posts/$postId'
    | '/users/$userId'
    | '/posts/'
    | '/users/'
    | '/_pathlessLayout/_nested-layout/route-a'
    | '/_pathlessLayout/_nested-layout/route-b'
    | '/posts_/$postId/deep'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  PathlessLayoutRoute: typeof PathlessLayoutRouteWithChildren
  DeferredRoute: typeof DeferredRoute
  PostsRoute: typeof PostsRouteWithChildren
  RedirectRoute: typeof RedirectRoute
  UsersRoute: typeof UsersRouteWithChildren
  PostsPostIdDeepRoute: typeof PostsPostIdDeepRoute
}
export interface FileServerRoutesByFullPath {
  '/customScript.js': typeof CustomScriptDotjsServerRoute
  '/api/users': typeof ApiUsersServerRouteWithChildren
  '/api/users/$userId': typeof ApiUsersUserIdServerRoute
}
export interface FileServerRoutesByTo {
  '/customScript.js': typeof CustomScriptDotjsServerRoute
  '/api/users': typeof ApiUsersServerRouteWithChildren
  '/api/users/$userId': typeof ApiUsersUserIdServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/customScript.js': typeof CustomScriptDotjsServerRoute
  '/api/users': typeof ApiUsersServerRouteWithChildren
  '/api/users/$userId': typeof ApiUsersUserIdServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/customScript.js' | '/api/users' | '/api/users/$userId'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/customScript.js' | '/api/users' | '/api/users/$userId'
  id: '__root__' | '/customScript.js' | '/api/users' | '/api/users/$userId'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  CustomScriptDotjsServerRoute: typeof CustomScriptDotjsServerRoute
  ApiUsersServerRoute: typeof ApiUsersServerRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/users': {
      id: '/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof UsersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/redirect': {
      id: '/redirect'
      path: '/redirect'
      fullPath: '/redirect'
      preLoaderRoute: typeof RedirectRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/posts': {
      id: '/posts'
      path: '/posts'
      fullPath: '/posts'
      preLoaderRoute: typeof PostsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deferred': {
      id: '/deferred'
      path: '/deferred'
      fullPath: '/deferred'
      preLoaderRoute: typeof DeferredRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_pathlessLayout': {
      id: '/_pathlessLayout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PathlessLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/users/': {
      id: '/users/'
      path: '/'
      fullPath: '/users/'
      preLoaderRoute: typeof UsersIndexRouteImport
      parentRoute: typeof UsersRoute
    }
    '/posts/': {
      id: '/posts/'
      path: '/'
      fullPath: '/posts/'
      preLoaderRoute: typeof PostsIndexRouteImport
      parentRoute: typeof PostsRoute
    }
    '/users/$userId': {
      id: '/users/$userId'
      path: '/$userId'
      fullPath: '/users/$userId'
      preLoaderRoute: typeof UsersUserIdRouteImport
      parentRoute: typeof UsersRoute
    }
    '/posts/$postId': {
      id: '/posts/$postId'
      path: '/$postId'
      fullPath: '/posts/$postId'
      preLoaderRoute: typeof PostsPostIdRouteImport
      parentRoute: typeof PostsRoute
    }
    '/_pathlessLayout/_nested-layout': {
      id: '/_pathlessLayout/_nested-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PathlessLayoutNestedLayoutRouteImport
      parentRoute: typeof PathlessLayoutRoute
    }
    '/posts_/$postId/deep': {
      id: '/posts_/$postId/deep'
      path: '/posts/$postId/deep'
      fullPath: '/posts/$postId/deep'
      preLoaderRoute: typeof PostsPostIdDeepRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_pathlessLayout/_nested-layout/route-b': {
      id: '/_pathlessLayout/_nested-layout/route-b'
      path: '/route-b'
      fullPath: '/route-b'
      preLoaderRoute: typeof PathlessLayoutNestedLayoutRouteBRouteImport
      parentRoute: typeof PathlessLayoutNestedLayoutRoute
    }
    '/_pathlessLayout/_nested-layout/route-a': {
      id: '/_pathlessLayout/_nested-layout/route-a'
      path: '/route-a'
      fullPath: '/route-a'
      preLoaderRoute: typeof PathlessLayoutNestedLayoutRouteARouteImport
      parentRoute: typeof PathlessLayoutNestedLayoutRoute
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/customScript.js': {
      id: '/customScript.js'
      path: '/customScript.js'
      fullPath: '/customScript.js'
      preLoaderRoute: typeof CustomScriptDotjsServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/users': {
      id: '/api/users'
      path: '/api/users'
      fullPath: '/api/users'
      preLoaderRoute: typeof ApiUsersServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/users/$userId': {
      id: '/api/users/$userId'
      path: '/$userId'
      fullPath: '/api/users/$userId'
      preLoaderRoute: typeof ApiUsersUserIdServerRouteImport
      parentRoute: typeof ApiUsersServerRoute
    }
  }
}

interface PathlessLayoutNestedLayoutRouteChildren {
  PathlessLayoutNestedLayoutRouteARoute: typeof PathlessLayoutNestedLayoutRouteARoute
  PathlessLayoutNestedLayoutRouteBRoute: typeof PathlessLayoutNestedLayoutRouteBRoute
}

const PathlessLayoutNestedLayoutRouteChildren: PathlessLayoutNestedLayoutRouteChildren =
  {
    PathlessLayoutNestedLayoutRouteARoute:
      PathlessLayoutNestedLayoutRouteARoute,
    PathlessLayoutNestedLayoutRouteBRoute:
      PathlessLayoutNestedLayoutRouteBRoute,
  }

const PathlessLayoutNestedLayoutRouteWithChildren =
  PathlessLayoutNestedLayoutRoute._addFileChildren(
    PathlessLayoutNestedLayoutRouteChildren,
  )

interface PathlessLayoutRouteChildren {
  PathlessLayoutNestedLayoutRoute: typeof PathlessLayoutNestedLayoutRouteWithChildren
}

const PathlessLayoutRouteChildren: PathlessLayoutRouteChildren = {
  PathlessLayoutNestedLayoutRoute: PathlessLayoutNestedLayoutRouteWithChildren,
}

const PathlessLayoutRouteWithChildren = PathlessLayoutRoute._addFileChildren(
  PathlessLayoutRouteChildren,
)

interface PostsRouteChildren {
  PostsPostIdRoute: typeof PostsPostIdRoute
  PostsIndexRoute: typeof PostsIndexRoute
}

const PostsRouteChildren: PostsRouteChildren = {
  PostsPostIdRoute: PostsPostIdRoute,
  PostsIndexRoute: PostsIndexRoute,
}

const PostsRouteWithChildren = PostsRoute._addFileChildren(PostsRouteChildren)

interface UsersRouteChildren {
  UsersUserIdRoute: typeof UsersUserIdRoute
  UsersIndexRoute: typeof UsersIndexRoute
}

const UsersRouteChildren: UsersRouteChildren = {
  UsersUserIdRoute: UsersUserIdRoute,
  UsersIndexRoute: UsersIndexRoute,
}

const UsersRouteWithChildren = UsersRoute._addFileChildren(UsersRouteChildren)

interface ApiUsersServerRouteChildren {
  ApiUsersUserIdServerRoute: typeof ApiUsersUserIdServerRoute
}

const ApiUsersServerRouteChildren: ApiUsersServerRouteChildren = {
  ApiUsersUserIdServerRoute: ApiUsersUserIdServerRoute,
}

const ApiUsersServerRouteWithChildren = ApiUsersServerRoute._addFileChildren(
  ApiUsersServerRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  PathlessLayoutRoute: PathlessLayoutRouteWithChildren,
  DeferredRoute: DeferredRoute,
  PostsRoute: PostsRouteWithChildren,
  RedirectRoute: RedirectRoute,
  UsersRoute: UsersRouteWithChildren,
  PostsPostIdDeepRoute: PostsPostIdDeepRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  CustomScriptDotjsServerRoute: CustomScriptDotjsServerRoute,
  ApiUsersServerRoute: ApiUsersServerRouteWithChildren,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
