/// <reference types="vite/client" />
import {
  <PERSON><PERSON>ontent,
  <PERSON>,
  Outlet,
  <PERSON>ripts,
  createRootRoute,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import * as React from "react";
import { DefaultCatchBoundary } from "~/components/DefaultCatchBoundary";
import { NotFound } from "~/components/NotFound";
import appCss from "~/styles/app.css?url";
import { seo } from "~/utils/seo";

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      ...seo({
        title: "Itinerarly | Planifică-ți călătoriile cu ușurință",
        description: `Itinerarly este aplicația perfectă pentru planificarea și organizarea itinerariilor de călătorie. Creează itinerarii personalizate și nu rata niciun moment important.`,
      }),
    ],
    links: [
      { rel: "stylesheet", href: appCss },
      {
        rel: "apple-touch-icon",
        sizes: "180x180",
        href: "/apple-touch-icon.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png",
      },
      { rel: "manifest", href: "/site.webmanifest", color: "#fffff" },
      { rel: "icon", href: "/favicon.ico" },
    ],
    scripts: [
      {
        src: "/customScript.js",
        type: "text/javascript",
      },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
});

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <div className="bg-white shadow-sm border-b">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between py-4">
              <Link
                to="/"
                className="text-2xl font-bold text-blue-600"
                activeOptions={{ exact: true }}
              >
                Itinerarly
              </Link>
              <nav className="flex gap-6 text-lg">
                <Link
                  to="/"
                  activeProps={{
                    className: "font-bold text-blue-600",
                  }}
                  activeOptions={{ exact: true }}
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Acasă
                </Link>
                <Link
                  to="/posts"
                  activeProps={{
                    className: "font-bold text-blue-600",
                  }}
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Itinerarii
                </Link>
                <Link
                  to="/users"
                  activeProps={{
                    className: "font-bold text-blue-600",
                  }}
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Profil
                </Link>
              </nav>
            </div>
          </div>
        </div>
        {children}
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  );
}
